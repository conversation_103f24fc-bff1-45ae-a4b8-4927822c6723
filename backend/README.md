# 物流訂單管理系統 API

一個完整的物流訂單管理系統後端API，包含用戶管理、訂單管理、權限控制、文件上傳和報表匯出功能。

## 功能特色

- 🔐 **用戶認證與授權** - JWT令牌認證，角色權限管理
- 📦 **訂單管理** - 完整的訂單CRUD操作，狀態追蹤
- 👥 **用戶管理** - 多角色用戶系統（管理員、經理、員工、檢視者）
- 📁 **文件上傳** - 支援多種文件格式上傳
- 📊 **報表匯出** - CSV格式報表匯出
- 🔒 **權限控制** - 細粒度權限控制系統

## 技術棧

- **Node.js** - 運行環境
- **Express.js** - Web框架
- **MongoDB** - 資料庫
- **Mongoose** - ODM
- **JWT** - 身份認證
- **Multer** - 文件上傳
- **bcryptjs** - 密碼加密

## 安裝與設置

### 1. 安裝依賴

```bash
npm install
```

### 2. 環境變數設置

複製 `.env` 文件並根據需要修改設置：

```bash
cp .env.example .env
```

### 3. 啟動MongoDB

確保MongoDB服務正在運行。

### 4. 創建管理員用戶

```bash
node scripts/createAdmin.js
```

### 5. 啟動服務

開發模式：
```bash
npm run dev
```

生產模式：
```bash
npm start
```

## API 端點

### 認證相關

- `POST /api/users/register` - 用戶註冊
- `POST /api/users/login` - 用戶登入
- `GET /api/users/profile` - 獲取用戶資料
- `PUT /api/users/profile` - 更新用戶資料
- `PUT /api/users/change-password` - 修改密碼

### 訂單管理

- `GET /api/orders` - 獲取訂單列表
- `POST /api/orders` - 創建新訂單
- `GET /api/orders/:id` - 獲取單一訂單
- `PUT /api/orders/:id` - 更新訂單
- `DELETE /api/orders/:id` - 刪除訂單
- `PATCH /api/orders/:id/status` - 更新訂單狀態
- `GET /api/orders/stats` - 獲取訂單統計

### 文件上傳

- `POST /api/orders/upload` - 上傳文件
- `POST /api/orders/:id/attachments` - 為訂單添加附件
- `DELETE /api/orders/upload/:filename` - 刪除文件

### 報表匯出

- `GET /api/orders/export/csv` - 匯出訂單CSV
- `GET /api/reports/orders` - 獲取訂單報表數據
- `GET /api/reports/users` - 獲取用戶報表數據
- `GET /api/reports/users/export/csv` - 匯出用戶CSV
- `GET /api/reports/dashboard` - 獲取儀表板數據

## 用戶角色與權限

### 角色類型

1. **admin（管理員）** - 擁有所有權限
2. **manager（經理）** - 可管理訂單和查看報表
3. **employee（員工）** - 可創建和編輯訂單
4. **viewer（檢視者）** - 只能查看訂單

### 權限矩陣

| 功能 | Admin | Manager | Employee | Viewer |
|------|-------|---------|----------|--------|
| 創建訂單 | ✅ | ✅ | ✅ | ❌ |
| 查看訂單 | ✅ | ✅ | ✅ | ✅ |
| 編輯訂單 | ✅ | ✅ | ✅ | ❌ |
| 刪除訂單 | ✅ | ❌ | ❌ | ❌ |
| 匯出報表 | ✅ | ✅ | ❌ | ❌ |
| 用戶管理 | ✅ | ❌ | ❌ | ❌ |

## 資料模型

### 用戶模型 (User)

```javascript
{
  username: String,
  email: String,
  password: String,
  fullName: String,
  role: String,
  permissions: Object,
  isActive: Boolean,
  lastLogin: Date,
  createdBy: ObjectId
}
```

### 訂單模型 (Order)

```javascript
{
  orderNumber: String,
  customerInfo: {
    name: String,
    email: String,
    phone: String,
    company: String
  },
  shippingAddress: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String
  },
  items: Array,
  orderTotal: {
    subtotal: Number,
    shippingCost: Number,
    tax: Number,
    total: Number
  },
  status: String,
  priority: String,
  shippingMethod: String,
  trackingNumber: String,
  attachments: Array,
  createdBy: ObjectId,
  statusHistory: Array
}
```

## 使用範例

### 登入

```bash
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123456"
  }'
```

### 創建訂單

```bash
curl -X POST http://localhost:3000/api/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "customerInfo": {
      "name": "張三",
      "email": "<EMAIL>",
      "phone": "0912345678"
    },
    "shippingAddress": {
      "street": "台北市信義區信義路五段7號",
      "city": "台北市",
      "state": "台北市",
      "zipCode": "110",
      "country": "Taiwan"
    },
    "items": [{
      "productName": "筆記型電腦",
      "productCode": "LAPTOP001",
      "quantity": 1,
      "unitPrice": 30000
    }]
  }'
```

## 開發說明

### 目錄結構

```
backend/
├── app.js              # 主應用程式
├── config/
│   └── db.js           # 資料庫配置
├── controllers/        # 控制器
├── middleware/         # 中間件
├── models/            # 資料模型
├── routes/            # 路由
├── utils/             # 工具函數
├── uploads/           # 上傳文件目錄
├── scripts/           # 腳本文件
└── .env              # 環境變數
```

### 錯誤處理

所有API回應都遵循統一格式：

```javascript
{
  "success": true/false,
  "message": "描述訊息",
  "data": {}, // 成功時的數據
  "error": "錯誤訊息" // 失敗時的錯誤
}
```

## 部署

### 生產環境設置

1. 設置環境變數
2. 使用PM2或類似工具管理進程
3. 配置反向代理（Nginx）
4. 設置SSL證書
5. 配置日誌輪轉

### Docker部署

```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 貢獻

歡迎提交Issue和Pull Request來改善這個項目。

## 授權

MIT License
