const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: [true, '用戶名稱為必填'],
    unique: true,
    trim: true,
    minlength: [3, '用戶名稱至少需要3個字符'],
    maxlength: [30, '用戶名稱不能超過30個字符']
  },
  email: {
    type: String,
    required: [true, '電子郵件為必填'],
    unique: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '請輸入有效的電子郵件地址']
  },
  password: {
    type: String,
    required: [true, '密碼為必填'],
    minlength: [6, '密碼至少需要6個字符']
  },
  fullName: {
    type: String,
    required: [true, '全名為必填'],
    trim: true,
    maxlength: [50, '全名不能超過50個字符']
  },
  role: {
    type: String,
    enum: ['admin', 'manager', 'employee', 'viewer'],
    default: 'employee',
    required: true
  },
  permissions: {
    orders: {
      create: { type: Boolean, default: false },
      read: { type: Boolean, default: true },
      update: { type: Boolean, default: false },
      delete: { type: Boolean, default: false },
      export: { type: Boolean, default: false }
    },
    users: {
      create: { type: Boolean, default: false },
      read: { type: Boolean, default: false },
      update: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    reports: {
      view: { type: Boolean, default: false },
      export: { type: Boolean, default: false }
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// 密碼加密中間件
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// 根據角色設置默認權限
userSchema.pre('save', function(next) {
  if (!this.isModified('role')) return next();
  
  switch (this.role) {
    case 'admin':
      this.permissions = {
        orders: { create: true, read: true, update: true, delete: true, export: true },
        users: { create: true, read: true, update: true, delete: true },
        reports: { view: true, export: true }
      };
      break;
    case 'manager':
      this.permissions = {
        orders: { create: true, read: true, update: true, delete: false, export: true },
        users: { create: false, read: true, update: false, delete: false },
        reports: { view: true, export: true }
      };
      break;
    case 'employee':
      this.permissions = {
        orders: { create: true, read: true, update: true, delete: false, export: false },
        users: { create: false, read: false, update: false, delete: false },
        reports: { view: false, export: false }
      };
      break;
    case 'viewer':
      this.permissions = {
        orders: { create: false, read: true, update: false, delete: false, export: false },
        users: { create: false, read: false, update: false, delete: false },
        reports: { view: false, export: false }
      };
      break;
  }
  next();
});

// 比較密碼方法
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// 檢查權限方法
userSchema.methods.hasPermission = function(resource, action) {
  return this.permissions[resource] && this.permissions[resource][action];
};

// 隱藏密碼字段
userSchema.methods.toJSON = function() {
  const userObject = this.toObject();
  delete userObject.password;
  return userObject;
};

module.exports = mongoose.model('User', userSchema);
