const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    required: true,
    unique: true,
    uppercase: true
  },
  customerInfo: {
    name: {
      type: String,
      required: [true, '客戶姓名為必填'],
      trim: true,
      maxlength: [100, '客戶姓名不能超過100個字符']
    },
    email: {
      type: String,
      required: [true, '客戶電子郵件為必填'],
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '請輸入有效的電子郵件地址']
    },
    phone: {
      type: String,
      required: [true, '客戶電話為必填'],
      match: [/^[\d\-\+\(\)\s]+$/, '請輸入有效的電話號碼']
    },
    company: {
      type: String,
      trim: true,
      maxlength: [100, '公司名稱不能超過100個字符']
    }
  },
  shippingAddress: {
    street: {
      type: String,
      required: [true, '街道地址為必填'],
      trim: true
    },
    city: {
      type: String,
      required: [true, '城市為必填'],
      trim: true
    },
    state: {
      type: String,
      required: [true, '州/省為必填'],
      trim: true
    },
    zipCode: {
      type: String,
      required: [true, '郵遞區號為必填'],
      trim: true
    },
    country: {
      type: String,
      required: [true, '國家為必填'],
      trim: true,
      default: 'Taiwan'
    }
  },
  items: [{
    productName: {
      type: String,
      required: [true, '產品名稱為必填'],
      trim: true
    },
    productCode: {
      type: String,
      required: [true, '產品代碼為必填'],
      trim: true,
      uppercase: true
    },
    quantity: {
      type: Number,
      required: [true, '數量為必填'],
      min: [1, '數量必須大於0']
    },
    unitPrice: {
      type: Number,
      required: [true, '單價為必填'],
      min: [0, '單價不能為負數']
    },
    totalPrice: {
      type: Number,
      required: true
    },
    weight: {
      type: Number,
      min: [0, '重量不能為負數']
    },
    dimensions: {
      length: Number,
      width: Number,
      height: Number
    }
  }],
  orderTotal: {
    subtotal: {
      type: Number,
      required: true,
      min: [0, '小計不能為負數']
    },
    shippingCost: {
      type: Number,
      default: 0,
      min: [0, '運費不能為負數']
    },
    tax: {
      type: Number,
      default: 0,
      min: [0, '稅額不能為負數']
    },
    total: {
      type: Number,
      required: true,
      min: [0, '總額不能為負數']
    }
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  shippingMethod: {
    type: String,
    enum: ['standard', 'express', 'overnight', 'pickup'],
    default: 'standard'
  },
  trackingNumber: {
    type: String,
    trim: true,
    uppercase: true
  },
  estimatedDelivery: {
    type: Date
  },
  actualDelivery: {
    type: Date
  },
  notes: {
    type: String,
    maxlength: [500, '備註不能超過500個字符']
  },
  attachments: [{
    filename: String,
    originalName: String,
    path: String,
    size: Number,
    mimetype: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  statusHistory: [{
    status: String,
    changedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    changedAt: {
      type: Date,
      default: Date.now
    },
    notes: String
  }]
}, {
  timestamps: true
});

// 自動生成訂單號碼
orderSchema.pre('save', async function(next) {
  if (!this.orderNumber) {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    // 查找今天的最後一個訂單號碼
    const prefix = `ORD${year}${month}${day}`;
    const lastOrder = await this.constructor.findOne({
      orderNumber: new RegExp(`^${prefix}`)
    }).sort({ orderNumber: -1 });
    
    let sequence = 1;
    if (lastOrder) {
      const lastSequence = parseInt(lastOrder.orderNumber.slice(-4));
      sequence = lastSequence + 1;
    }
    
    this.orderNumber = `${prefix}${sequence.toString().padStart(4, '0')}`;
  }
  next();
});

// 計算商品總價
orderSchema.pre('save', function(next) {
  // 計算每個商品的總價
  this.items.forEach(item => {
    item.totalPrice = item.quantity * item.unitPrice;
  });
  
  // 計算訂單總計
  this.orderTotal.subtotal = this.items.reduce((sum, item) => sum + item.totalPrice, 0);
  this.orderTotal.total = this.orderTotal.subtotal + this.orderTotal.shippingCost + this.orderTotal.tax;
  
  next();
});

// 添加狀態變更到歷史記錄
orderSchema.pre('save', function(next) {
  if (this.isModified('status') && !this.isNew) {
    this.statusHistory.push({
      status: this.status,
      changedBy: this.updatedBy,
      changedAt: new Date()
    });
  }
  next();
});

// 索引
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ 'customerInfo.email': 1 });
orderSchema.index({ status: 1 });
orderSchema.index({ createdAt: -1 });
orderSchema.index({ trackingNumber: 1 });

module.exports = mongoose.model('Order', orderSchema);
