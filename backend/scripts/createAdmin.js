const mongoose = require('mongoose');
const User = require('../models/User');
const dotenv = require('dotenv');

// 載入環境變數
dotenv.config();

const createAdminUser = async () => {
  try {
    // 連接資料庫
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/order_management', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // 檢查是否已存在管理員用戶
    const existingAdmin = await User.findOne({ role: 'admin' });
    
    if (existingAdmin) {
      console.log('Admin user already exists:', existingAdmin.email);
      process.exit(0);
    }

    // 創建管理員用戶
    const adminUser = new User({
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123456',
      fullName: '系統管理員',
      role: 'admin',
      isActive: true
    });

    await adminUser.save();

    console.log('Admin user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123456');
    console.log('Please change the password after first login.');

  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await mongoose.connection.close();
    process.exit(0);
  }
};

createAdminUser();
