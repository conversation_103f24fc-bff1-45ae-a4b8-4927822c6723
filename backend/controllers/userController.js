const User = require('../models/User');
const { generateToken } = require('../middleware/auth');

// 用戶註冊
const register = async (req, res) => {
  try {
    const { username, email, password, fullName, role } = req.body;

    // 檢查用戶是否已存在
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用戶名稱或電子郵件已存在'
      });
    }

    // 創建新用戶
    const user = new User({
      username,
      email,
      password,
      fullName,
      role: role || 'employee',
      createdBy: req.user ? req.user._id : null
    });

    await user.save();

    // 生成JWT令牌
    const token = generateToken(user._id);

    res.status(201).json({
      success: true,
      message: '用戶註冊成功',
      data: {
        user: user.toJSON(),
        token
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: '註冊失敗',
      error: error.message
    });
  }
};

// 用戶登入
const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // 驗證輸入
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: '請提供電子郵件和密碼'
      });
    }

    // 查找用戶
    const user = await User.findOne({ email }).select('+password');
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '電子郵件或密碼錯誤'
      });
    }

    // 檢查帳戶是否活躍
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: '帳戶已被停用'
      });
    }

    // 驗證密碼
    const isPasswordValid = await user.comparePassword(password);
    
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '電子郵件或密碼錯誤'
      });
    }

    // 更新最後登入時間
    user.lastLogin = new Date();
    await user.save();

    // 生成JWT令牌
    const token = generateToken(user._id);

    res.json({
      success: true,
      message: '登入成功',
      data: {
        user: user.toJSON(),
        token
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: '登入失敗',
      error: error.message
    });
  }
};

// 獲取當前用戶資訊
const getProfile = async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        user: req.user
      }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: '獲取用戶資訊失敗',
      error: error.message
    });
  }
};

// 更新用戶資訊
const updateProfile = async (req, res) => {
  try {
    const { fullName, email } = req.body;
    const userId = req.user._id;

    // 檢查電子郵件是否已被其他用戶使用
    if (email && email !== req.user.email) {
      const existingUser = await User.findOne({ email, _id: { $ne: userId } });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '電子郵件已被其他用戶使用'
        });
      }
    }

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { fullName, email },
      { new: true, runValidators: true }
    );

    res.json({
      success: true,
      message: '用戶資訊更新成功',
      data: {
        user: updatedUser
      }
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: '更新用戶資訊失敗',
      error: error.message
    });
  }
};

// 修改密碼
const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user._id;

    // 驗證輸入
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: '請提供當前密碼和新密碼'
      });
    }

    // 查找用戶
    const user = await User.findById(userId).select('+password');
    
    // 驗證當前密碼
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: '當前密碼錯誤'
      });
    }

    // 更新密碼
    user.password = newPassword;
    await user.save();

    res.json({
      success: true,
      message: '密碼修改成功'
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: '修改密碼失敗',
      error: error.message
    });
  }
};

// 獲取所有用戶（管理員功能）
const getAllUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, role, isActive } = req.query;
    
    const filter = {};
    if (role) filter.role = role;
    if (isActive !== undefined) filter.isActive = isActive === 'true';

    const users = await User.find(filter)
      .populate('createdBy', 'username fullName')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await User.countDocuments(filter);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / limit),
          total
        }
      }
    });
  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({
      success: false,
      message: '獲取用戶列表失敗',
      error: error.message
    });
  }
};

module.exports = {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword,
  getAllUsers
};
