const express = require('express');
const router = express.Router();
const {
  createOrder,
  getOrders,
  getOrderById,
  updateOrder,
  deleteOrder,
  updateOrderStatus,
  getOrderStats
} = require('../controllers/orderController');  // 從 orderController.js 檔案中「解構出多個 export 的函式」並直接定義成變數使用。
const {
  authenticateToken,
  checkPermission
} = require('../middleware/auth');

// 所有路由都需要認證
router.use(authenticateToken);

// 基本路由測試
router.get('/', checkPermission('orders', 'read'), getOrders);
router.post('/', checkPermission('orders', 'create'), createOrder);
router.get('/stats', checkPermission('orders', 'read'), getOrderStats);
router.get('/:id', checkPermission('orders', 'read'), getOrderById);
router.put('/:id', checkPermission('orders', 'update'), updateOrder);
router.delete('/:id', checkPermission('orders', 'delete'), deleteOrder);
router.patch('/:id/status', checkPermission('orders', 'update'), updateOrderStatus);

module.exports = router;
