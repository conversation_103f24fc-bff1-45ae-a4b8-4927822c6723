const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const dotenv = require('dotenv');
const path = require('path');

// 載入環境變數
dotenv.config();

// 導入路由
const orderRoutes = require('./routes/orderRoutes_backup');
const userRoutes = require('./routes/userRoutes');
const reportRoutes = require('./routes/reportRoutes');

// 導入資料庫連接
const connectDB = require('./config/db');

const app = express();

// 連接資料庫
connectDB();

// 中間件
app.use(helmet()); // 安全性中間件
app.use(cors()); // 跨域請求
app.use(morgan('combined')); // 日誌記錄
app.use(express.json({ limit: '10mb' })); // 解析JSON
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // 解析URL編碼

// 靜態文件服務 - 用於上傳的文件
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 路由
app.use('/api/orders', orderRoutes);
app.use('/api/users', userRoutes);
app.use('/api/reports', reportRoutes);

// 根路由
app.get('/', (req, res) => {
  res.json({
    message: 'Order Management System API',
    version: '1.0.0',
    endpoints: {
      orders: '/api/orders'
    }
  });
});

// 404 錯誤處理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found'
  });
});

// 全域錯誤處理中間件
app.use((err, req, res, next) => {
  console.error(err.stack);
  
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal Server Error',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
});

module.exports = app;
